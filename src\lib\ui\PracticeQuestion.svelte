<script lang="ts">
	import { innerWidth } from 'svelte/reactivity/window';
	import { P1, P2, P3 } from ".";
	import MarkBarUI from './MarkBarUI.svelte';
    import QuestionHeader from './QuestionHeader.svelte';
    import { annotateManager } from '$lib/annotate/annotateManager.js';

    let {
        i = 0,
        intro,
        passage,
        passage2,
        question,
        correctAnswer,
        answerChoices,
        explanation,
        onAnswerSelect,
        reset,
        updateMarkedInDB = () => {},
        showCorrectAnswer = false,
        showExplanation = false,
        isMarked = $bindable(false),
        onAnnotateClick,
        annotateIntro = $bindable(null),
        annotatePassage = $bindable(null),
        annotatePassage2 = $bindable(null),
        annotateExplanation = $bindable(null),
        annoButton = $bindable(null)
    } = $props();

    const letters = ['A', 'B', 'C', 'D'];
    let selectedAnswer = $state<number | null>(null);
    let isAnswered = $state(false);
    let isCrossedOut = $state(Array(answerChoices.length).fill(false));

    let setMarked = () => {
        isMarked = !isMarked;
        updateMarkedInDB();
    }

    let isEliminateToolActive = $state(true);

    function convertMarkdownItalic(text: string): string {
        return text.replace(/\*(.*?)\*/g, '<i>$1</i>');
    }

    $effect(() => {
        // Reset when either the reset prop changes or the question changes
        if (reset || question) {
            selectedAnswer = null;
            isAnswered = false;
            isCrossedOut = Array(answerChoices.length).fill(false);
        }
    });

    $effect(() => {
        if (showCorrectAnswer) {
            selectedAnswer = correctAnswer;
            isAnswered = true;
        }
    });

    function handleAnswerSelect(index: number) {
        if (isAnswered) return;
        isCrossedOut = Array(answerChoices.length).fill(false);
        selectedAnswer = index;
        isAnswered = true;
        onAnswerSelect?.(index);
    }

    function handleCrossOut(index: number) {
        if (isAnswered) return;
        isCrossedOut = isCrossedOut.map((val, i) => i === index ? !val : val);
    }

    function isCorrect(index: number) {
        return index === correctAnswer;
    }

    function getButtonClass(index: number) {
        let base = 'question-choice';
        if (isCrossedOut[index]) base += ' question-choice-crossed';
        if (!isAnswered) return base;
        if (isCorrect(index)) return base + ' question-choice-correct';
        if (selectedAnswer === index && !isCorrect(index)) return base + ' question-choice-incorrect';
        return base;
    }



    // Annotate
    let introElement = $state(null);
    let passageElement = $state(null);
    let passage2Element = $state(null);
    let explanationElement = $state(null);
    let maxI = $state(0);

    $effect(() => {
        if (i > maxI) {
            annotatePassage?.destroy();
            annotatePassage = annotateManager(passageElement, "passage");

            annotateIntro?.destroy();
            annotateIntro = annotateManager(introElement, "intro");

            annotatePassage2?.destroy();
            annotatePassage2 = annotateManager(passage2Element, "passage2");

            annotateExplanation = annotateManager(explanationElement, "explanation");
            annotateExplanation?.destroy();
            
            maxI = i;
        }
    });

    $effect(() => {
        if (annotatePassage && passageElement) {
            annotatePassage.setRoot(passageElement);
        }
    });

    $effect(() => {
        if (annotateIntro && introElement) {
            annotateIntro.setRoot(introElement);
        }
    });

    $effect(() => {
        if (annotatePassage2 && passage2Element) {
            annotatePassage2.setRoot(passage2Element);
        }
    });

    $effect(() => {
        if (annotateExplanation && explanationElement) {
            annotateExplanation.setRoot(explanationElement);
        }
    });


</script>

<div class="question-wrapper">
    <QuestionHeader {onAnnotateClick} bind:annoButton questionIndex={i} />
    <div class="question-container">
        {#if showExplanation}
            <div class="explanation-container">
                <P2><p bind:this={explanationElement}>{@html convertMarkdownItalic(explanation)}</p></P2>
            </div>
        {:else}
            <div class="question">

                <!-- R&W Questions -->
                {#key i}  <!-- Recreate the passage wrapper when the quesion changes -->
                <div class="passage-wrapper">
                    {#if intro}
                        <P3><p bind:this={introElement}>{@html convertMarkdownItalic(intro)}</p></P3>
                    {/if}

                    {#if passage2}
                        <P1 isBold={true}>Text 1</P1>   
                    {/if}

                    {#if passage2 && innerWidth.current < 1024 && innerWidth.current > 768}
                        <P3><p bind:this={passageElement}>{@html convertMarkdownItalic(passage)}</p></P3>
                    {:else}
                        <P2><p bind:this={passageElement}>{@html convertMarkdownItalic(passage)}</p></P2>
                    {/if}

                    {#if passage2}
                        <br>
                        <P1 isBold={true}>Text 2</P1>
                    {/if}

                    {#if passage2 && innerWidth.current < 1024 && innerWidth.current > 768}
                        <P3><p bind:this={passage2Element}>{@html convertMarkdownItalic(passage2)}</p></P3>   
                    {:else}
                        <P2><p bind:this={passage2Element}>{@html convertMarkdownItalic(passage2)}</p></P2>
                    {/if}
                </div>
                {/key}

                <div class="vr"></div>
                <hr />

                <div class="question-right">
                    <MarkBarUI
                        i={i - 1} 
                        isSPR={false}
                        toggleElimination={() => isEliminateToolActive = !isEliminateToolActive} 
                        isEliminateTool={isEliminateToolActive}
                        {isMarked}
                        {setMarked}

                    />
                    <!-- MCQ -->
                    <P2>{@html convertMarkdownItalic(question)}</P2>
                    {#each answerChoices as choice, index}
                        <div class="question-choice-wrapper">
                            <button
                                class={getButtonClass(index) + ' question-choice-flex'}
                                onclick={() => handleAnswerSelect(index)}
                                disabled={isAnswered || isCrossedOut[index]}
                            >
                                <div class="question-choice-letter">
                                    <P2>{letters[index]}</P2>
                                </div>
                                <div class="question-choice-text">
                                    {#if choice.length < 200}
                                        <P2>{@html convertMarkdownItalic(choice)}</P2>
                                    {:else}
                                        <P3>{@html convertMarkdownItalic(choice)}</P3>
                                    {/if}
                                </div>
                            </button>
                            {#if isEliminateToolActive}
                            <button
                                class="crossout-btn"
                                aria-label={isCrossedOut[index] ? `Undo cross out for answer ${letters[index]}` : `Cross out answer ${letters[index]}`}
                                onclick={() => handleCrossOut(index)}
                                disabled={isAnswered}
                                type="button"
                            >
                                {#if isCrossedOut[index]}
                                    <P3 isBold={true}><u>Undo</u></P3>
                                {:else}
                                    <span aria-hidden="true" class="cross-choice">{letters[index]}</span>
                                {/if}
                                </button>
                            {/if}
                        </div>
                    {/each}                
                </div>
            </div>
        {/if}
    </div>
</div>

<style>
    .question-wrapper {
        display: flex;
        flex: 1 1 0%;
        height: 100%;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        border-radius: 0.75rem;
        border: 1.5px solid var(--pitch-black, #000);
        background-color: var(--white);
        box-shadow: 0.25rem 0.25rem 0px var(--pitch-black, #000);
    }

    .question-container {
        display: flex;
        flex-direction: column;
        gap: 10px;
        flex: 1 1 0%;
        height: 100%;
        padding: 2rem;
        overflow-y: auto;
		scrollbar-color: var(--sky-blue) var(--light-sky-blue);
		scrollbar-width: thin;
    }

    .question {
        width: 100%;
        display: flex;
        align-items: flex-start;
        gap: 1rem;
        flex: 1 1 0%;
        height: 100%;
    }

    .passage-wrapper {
        width: 100%;
        display: flex;
        flex-direction: column;
        color: var(--pitch-black, #000);
        line-height: 1.6875rem;
    }

    .vr {
        align-self: stretch;
        width: 1px;
        background: #000;
    }

    hr {
        display: none;
    }

    .question-right {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
        width: 100%;
        padding-bottom: 2rem;
    }

    .question-choice-wrapper {
        display: flex;
        align-items: center;
        width: 100%;
        gap: 1rem;
    }

    .question-choice {
        display: flex;
        align-items: center;
        border: 1px solid #000;
        border-radius: 0.5rem;
        box-shadow: 0.25rem 0.25rem 0px var(--pitch-black, #000);
        background: #FFF;
        width: 100%;
    }

    .question-choice:active:enabled {
        box-shadow: none;
        transform: translate(0.25rem, 0.25rem);
    }

    .question-choice-incorrect:disabled {
        background: var(--rose, #EB47AB);
        cursor: not-allowed;
    }

    .question-choice-correct {
        background: var(--aquamarine, #55ECB2);
    }

    .question-choice-selected {
        background: var(--sky-blue, #66E2FF);
    }

    .question-choice-letter {
        display: flex;
        width: 50px;
        padding: 10px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        color: #000;

        line-height: 27px; /* 150% */
    }

    .question-choice-text {
        text-align: start;
        width: 100%;    
        padding: 0.75rem;
        border-left: 1px solid var(--pitch-black, #000);
        line-height: 1.6875rem;
    }

    .crossout-btn {
        height: fit-content;
    }

    .undo-cross {
        text-decoration-line: underline;
    }

    .cross-choice {
        border: 1.5px solid #000;
        border-radius: 50%;

        font-size: 0.9rem;
        font-family: 'Inter';
        font-weight: 600;
        line-height: 25.1px; /* 167.333% */

        width: 1.5rem;
        height: 1.5rem;
        margin: 0 0.52rem;
        display: flex;
        align-items: center;
        justify-content: center;

        position: relative;
    }

    .cross-choice:after {
        content: '';
        position: absolute;
        width: 150%;
        background-color: black;
        height: 2px;
        top: 50%;
        translate: 0 -50%;    
    }
    
    @media (max-width: 768px) {
        .question {
            flex-direction: column;
            gap: 10px;
        }

        .vr {
            display: none;
        }

        hr {
            display: block;
            border: 1px solid var(--pitch-black, #000);
            width: 100%;
        }

        .question-right {
            gap: 10px;
        }
    }

    @media (max-width: 540px) {
        .question-wrapper {
            padding: 1rem;
            flex: initial;
            height: calc(100vh - 9rem);
        }
    }

    .question-choice-crossed {
        opacity: 0.4;
        position: relative;
    }

    .question-choice-crossed:after {
        content: '';
        position: absolute;
        width: 104%;
        background-color: black;
        height: 2px;
        top: 50%;
        translate: -1.5% -50%;    
    }

    .crossout-btn {
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 50%;
        transition: background 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .crossout-btn:disabled, .question-choice:disabled {
        cursor: not-allowed;
        opacity: 0.5;
    }

    .question-choice-flex {
        flex: 1 1 0%;
    }
</style>

