<script lang="ts">
	import { user } from '$lib/firebase/auth.svelte.ts';
	import type { Question, Difficulty, QuestionType, CompletedQuestion } from '$lib/types/index';
	import { H5, Button, Checkbox, P1, P2, PracticeQuestion } from '$lib/ui';
	import posthog from 'posthog-js';
	import { innerWidth } from 'svelte/reactivity/window';
	import { db } from '$lib/firebase/firestore';
	import { doc, arrayUnion, increment, arrayRemove, onSnapshot, writeBatch, WriteBatch } from 'firebase/firestore';
	import Dropdown from '$lib/ui/Dropdown.svelte';	
	import { browser } from '$app/environment';
	import { onDestroy, onMount } from 'svelte';
	import { debounce } from '$lib/utilities.js';

	let { data } = $props();

	// Initialize difficulty filters with their states and colors
	let difficulties = $state<{ label: Difficulty, checked: boolean, color: string }[]>([
		{ label: 'Easy', checked: true, color: 'var(--aquamarine)' },
		{ label: 'Medium', checked: true, color: 'var(--yellow)' },
		{ label: 'Hard', checked: true, color: 'var(--rose)' }
	]);

	// Initialize question type filters
	let questionTypes = $state<{ label: QuestionType | "All", checked: boolean }[]>([
		{ label: 'All', checked: true },
		{ label: 'Word in Context', checked: false },
		{ label: 'Main Idea', checked: false },
		{ label: 'Specific Detail', checked: false },
		{ label: 'Main Purpose', checked: false },
		{ label: 'Overall Structure', checked: false },
		{ label: 'Inference', checked: false },
		{ label: 'Command of Evidence', checked: false },
		{ label: 'Paired Passage', checked: false },
	]);
	
	// Effect to automatically check "All" when all individual question type is selected
	$effect(() => {
		if (questionTypes.slice(1).some(type => type.checked)) {
			questionTypes[0].checked = false;
		}
	})

	$effect(() => {
		if (questionTypes[0].checked) {
			questionTypes.slice(1).forEach(type => type.checked = false);
		}
	})

	type DropdownText = "All Questions" | "Questions I Have Not Encountered" | "Questions I Got Wrong" | "Questions Marked for Review";

	// Snapshot for preserving state between page navigations
	export const snapshot = {
		capture: () => {
			return {
				difficulties,
				questionTypes,
				dropdownText,
			}
		},
		restore: (value) => {
			difficulties = value.difficulties;
			questionTypes = value.questionTypes;
			dropdownText = value.dropdownText;
		}
	};

	// Component state variables
	let loading = $state(false);
	let questionCount = $state(0);
	let question: Question | null = $state(null);
	let showCorrectAnswer = $state(false);
	let isPopupOpen = $state(false);
	let questionScene: HTMLDivElement | null = $state(null);
	let answerState = $state<'correct' | 'incorrect' | 'unanswered'>('unanswered');
	let isAnswerUnanswered = $derived(answerState === 'unanswered');
	let isExplanationVisible = $state(false);
	let wasAnswersCorrect = $state<boolean[]>([]);
	let resetQuestion = $state(false);

	const dropdownChoices: DropdownText[] = ["All Questions", "Questions I Have Not Encountered", "Questions I Got Wrong", "Questions Marked for Review"];
	let dropdownText = $state(dropdownChoices[0]);
	let dropdownChoiceIndex = $derived(dropdownChoices.indexOf(dropdownText));
	
	// Track user progress with PostHog analytics
	$effect(() => {
		if (wasAnswersCorrect.length === 5) {
			posthog.capture('5_question_completed');
		}

		if (wasAnswersCorrect.length === 7) {
			posthog.capture('7_questions_completed');
		}
	})

	// Toggle difficulty selection
	function toggleDifficulty(i: number) {
		difficulties[i].checked = !difficulties[i].checked;
	}

	let batch = $state<WriteBatch>(writeBatch(db));

	// Debounced Firestore update for Marked for Review
	async function updateMarkedForReview(marked: boolean) {
		if ($user && question) {
			try {
				const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', "dataDoc");
				const update = marked
					? { markedQuestions: arrayUnion(question.id) }
					: { markedQuestions: arrayRemove(question.id) };
				batch.update(completedQuestionsRef, update);
			} catch (e) {
				console.error('Failed to update marked for review:', e);
			}
		}
	}

	const debouncedUpdateMarkedForReview = debounce(updateMarkedForReview, 900);

	// Fetch a new question from the API based on selected filters
	async function start() {
		loading = true;
		const chosenDifficulties = difficulties.filter(d => d.checked).map(d => d.label);
		let chosenTypes: QuestionType[] = [];
		
		if (questionTypes[0].checked) {
			chosenTypes = questionTypes.filter(t => t.label !== "All").map(t => t.label as QuestionType);
		} else {
			chosenTypes = questionTypes.filter(t => t.checked && t.label !== "All").map(t => t.label as QuestionType);
		}

		const headers: Record<string, string> = {
			'Content-Type': 'application/json'
		};

		if ($user) {
			headers['Authorization'] = `Bearer ${await $user.getIdToken()}`;
		}

		const body: {
			chosenDifficulties: Difficulty[],
			chosenTypes: QuestionType[],
			excludedQuestionIds?: number[],
			questionIds?: number[]
		} = {
			chosenDifficulties, 
			chosenTypes,
		}

		switch(dropdownChoiceIndex) {
			case 1:
				body.excludedQuestionIds = completedQuestions
				break;
			case 2:
				body.questionIds = incorrectlyAnsweredQuestions
				break;
			case 3:
				body.questionIds = markedQuestions
				break;
		};

		const response = await fetch('/api/get-question', {
			method: 'POST',
			headers,
			body: JSON.stringify(body)
		});

		if (response.ok) {
			const res = await response.json();
			if (res.question) {
				question = res.question;
				isMarked = markedQuestions.includes(question.id);
			} else {
				alert(res.message);
			}
			questionCount += 1;
		} else if (response.status === 429) {
			isPopupOpen = true;
		} else {
			question = null;
			alert((await response.json()).message);
		}

		loading = false;
	}

	$effect(() => {
		if (questionScene && question) {
			questionScene.scrollIntoView(true);
		}
	})

	// Handle answer selection and update state
	async function handleAnswerSelect(index: number) {
		if (!isAnswerUnanswered) return;
		const isCorrect = index === question?.correctAnswer;
		answerState = isCorrect ? 'correct' : 'incorrect';
		wasAnswersCorrect = [...wasAnswersCorrect, isCorrect];

		
		// Track user progress in users/{id}/completedQuestions
		if ($user) {
			try {
				// Store the question data in a doc to reduce the number of reads. Kinda cope but will change if we get enough users.
				const completedQuestionsRef = doc(db, 'users', $user.uid, 'completedQuestions', "dataDoc");
				
				const newDocData = {
					data: arrayUnion({
						question: question.id,
						wasAnswerCorrect: isCorrect,
						studentAnswer: index,
						timestamp: new Date().toISOString(),
					}),
					[`stats.${question.questionType}.total`]: increment(1),
					[`stats.${question.questionType}.correct`]: isCorrect ? increment(1) : increment(0),
					incorrectlyAnsweredQuestions: isCorrect ? arrayRemove(question.id) : arrayUnion(question.id),
				}
				
				batch.update(completedQuestionsRef, newDocData);
			} catch (e) {
				console.error('Failed to add completedQuestion:', e);
			}
		}
	}

	// Toggle explanation visibility
	function showExplanation() {
		isExplanationVisible = !isExplanationVisible;
	}

	// Load next question and reset states
	async function nextQuestion() {
		batch.commit();
		batch = writeBatch(db);
		await start();
		isExplanationVisible = false;
		answerState = 'unanswered';
		showCorrectAnswer = false;
		resetQuestion = !resetQuestion;
		// Scroll question wrapper back to top
		const questionWrapper = document.querySelector('.question-wrapper');
		if (questionWrapper) {
			questionWrapper.scrollTop = 0;
		}
	}

	let isMarked = $state(false);

	// Update in realtime
	let dataDoc: CompletedQuestion | null = $state(null);
	let { data: completedQuestionData, stats, markedQuestions, incorrectlyAnsweredQuestions } = $derived(dataDoc ?? {
		data: [],
		stats: {},
		markedQuestions: [],
		incorrectlyAnsweredQuestions: []
	} as CompletedQuestion);
	let completedQuestions = $derived(completedQuestionData.map(d => d.question))
	let unsubscribeCompletedQuestions = null;

	if (browser && data?.uid) {
		const completedQuestionsRef = doc(db, 'users', data.uid, 'completedQuestions', 'dataDoc');
		unsubscribeCompletedQuestions = onSnapshot(completedQuestionsRef, (snapshot) => {
			dataDoc = snapshot.exists() ? (snapshot.data() as CompletedQuestion) : null;
		});
	}

	// Clean up the listener when the component is destroyed
	onDestroy(() => {
		if (unsubscribeCompletedQuestions) unsubscribeCompletedQuestions();
	});


	// Annotate
	import { selectedRange, selectedName } from '$lib/annotate/annotateManager';

	let isAnnotate = $state(false);
	let selectedText: string | null = $state(null);
	let annoText: string | null = $state(null);
	let progressContainer: HTMLDivElement | null = $state(null);
	let annoButton: HTMLButtonElement | null = $state(null);

	let annotateIntro = $state(null);
	let annotatePassage = $state(null);
	let annotatePassage2 = $state(null);
	let annotateExplanation = $state(null);
	let annotateElements = $derived({
		intro: annotateIntro,
		passage: annotatePassage,
		passage2: annotatePassage2,
		explanation: annotateExplanation
	});

	// Alert if no selection is made when annotate is clicked
	const annotateNoSelectionAlert = debounce(() => {
		isAnnotate = false;
	}, 1500);


	function truncateText(text: string): string {
		if (text.length >= 100) {
			let l = 39,
				r = text.length - 40;
			let ar = [' ', '.', ',', ';'];
			while (!ar.includes(text[l])) l++;
			while (!ar.includes(text[r])) r--;
			text = text.slice(0, l) + '...' + text.slice(r + 1);
		}
		return text;
	}

	function openAnnotate() {
		isAnnotate = true;
		annoText = annotateElements[$selectedName]?.getAnnoText();
		selectedText = annotateElements[$selectedName]?.getSelectedText();
	}

	function closeAnnotate() {
		isAnnotate = false;
		annoText = null;
		selectedText = null;
		annotateElements[$selectedName]?.setSelected(null, null);
		selectedName.set(null);
	}

	function onAnnotateClick() {
		if (!isAnnotate) {
			for(const key in annotateElements) {
				if (annotateElements[key]?.highlight()) {
					openAnnotate();
					return;
				}
			}
			isAnnotate = true;
			annotateNoSelectionAlert();
		} else {
			closeAnnotate();
		}
	}

	function onDeleteClick() {
		annotateElements[$selectedName]?.deleteHighlight();
	}

	// Update annotate text for current selected highlight
	$effect(() => {
		if (annoText !== null) {
			annotateElements[$selectedName]?.setAnnoText(annoText);
		}
	})

	// Open annotate when a highlight is selected
	$effect(() => {
		if ($selectedRange[0]) {
			openAnnotate();
		}
	});

	// Close annotate when clicking outside
	onMount(() => {
		document.addEventListener("click", (event: MouseEvent) => {
			const target = event.target as HTMLElement;

			// Ignore clicks on <mark> elements
			if (target.tagName === "MARK") return;

			// Ignore clicks on annotate button
			if (annoButton && annoButton.contains(target)) return;

			if (!progressContainer?.contains(target)) {
				closeAnnotate();
			}
		});
	});
</script>

{#if !question}
<!-- Initial question bank setup screen -->
<div class="qb-root">
	<!-- Difficulty selection row -->
	<div class="qb-difficulty-row">
		<P1 isBold={true}>Difficulty:</P1>
		{#each difficulties as difficulty, i}
			<Button 
				isSecondary={!difficulty.checked} 
				onclick={() => toggleDifficulty(i)}
				--button-bg-color={difficulty.color}
			>
				{difficulty.label}
			</Button>
		{/each}
	</div>

	<div class="dropdown-row">
		<P1 isBold={true}>Include:</P1>
		<Dropdown bind:dropdownText {dropdownChoices} />
	</div>

	<!-- Question type and topic selection containers -->
	<div class="qb-selectors-row">
		<div class="qb-selector">
			<div class="qb-selector-header">
				<P1 isBold={true}>Select Question Type(s)</P1>
				<P1 isBold={true}>Correct Rate</P1>
			</div>
			<hr />
			<div class="qb-selector-list">
				{#each questionTypes as type, i}
					{@const stat = stats?.[type.label]}
					<div class="qb-checkbox-row">
						<Checkbox label={type.label} bind:isChecked={type.checked} />
						{#if i === 0}
							{@const allCorrect = Object.values(stats).reduce((acc, stat) => acc + stat.correct, 0)}
							{@const allTotal = Object.values(stats).reduce((acc, stat) => acc + stat.total, 0)}
							{@const allPercentage = Math.round(allCorrect / allTotal  * 100)}
							<P2>{allPercentage ? (allPercentage + "%") : "~" }</P2>		
						{:else}
							<P2>{stat?.total ? (Math.round((stat.correct / stat.total) * 100) + "%") : "~"}</P2>
						{/if}
					</div>
				{/each}
			</div>
		</div>
	</div>

	{#if $user}
		<Button disabled={loading} onclick={start}>Start!</Button>
	{:else}
		<a href="/sign-up?redirectTo=/question-bank"><Button>🔒 Sign Up to Start 🔒</Button></a>
	{/if}
</div>
{:else}
<!-- Question practice screen -->
<div class="qb-root question-scene" bind:this={questionScene}>
	<PracticeQuestion
		i={questionCount}
		{...question}
		onAnswerSelect={handleAnswerSelect}
		reset={resetQuestion}
		showCorrectAnswer={showCorrectAnswer}
		showExplanation={isExplanationVisible}
		updateMarkedInDB={() => debouncedUpdateMarkedForReview(isMarked)}
		bind:isMarked
		{onAnnotateClick}
		bind:annotateIntro
		bind:annotatePassage
		bind:annotatePassage2
		bind:annotateExplanation
		bind:annoButton
	/>
	<!-- Progress and navigation container -->
	<div class="progress-container" bind:this={progressContainer}>
		{#if !isAnnotate}
			{#if innerWidth.current > 540}
				{@render top()}
				{@render progress()}
				{@render navButtons()}
			{:else}
				<div class="top-mobile-container">
					{@render top()}
					{@render navButtons()}
				</div>
			{/if}
		{:else}
			{@render annotatePanel()}
		{/if}

	</div>
</div>
{/if}

<!-- Back button snippet -->
{#snippet top()}
	<div class="top">
		<button onclick={() => {question = null}} aria-label="Back">
			<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M13.8168 18.8163C13.6651 18.9748 13.5462 19.1618 13.4668 19.3663C13.3001 19.7721 13.3001 20.2272 13.4668 20.633C13.5462 20.8376 13.6651 21.0245 13.8168 21.183L18.8168 26.183C19.1307 26.4968 19.5563 26.6732 20.0002 26.6732C20.444 26.6732 20.8697 26.4968 21.1835 26.183C21.4973 25.8692 21.6736 25.4435 21.6736 24.9997C21.6736 24.5558 21.4973 24.1302 21.1835 23.8163L19.0168 21.6663H25.0002C25.4422 21.6663 25.8661 21.4907 26.1787 21.1782C26.4912 20.8656 26.6668 20.4417 26.6668 19.9997C26.6668 19.5576 26.4912 19.1337 26.1787 18.8212C25.8661 18.5086 25.4422 18.333 25.0002 18.333H19.0168L21.1835 16.183C21.3397 16.0281 21.4637 15.8437 21.5483 15.6406C21.6329 15.4375 21.6765 15.2197 21.6765 14.9997C21.6765 14.7797 21.6329 14.5618 21.5483 14.3587C21.4637 14.1556 21.3397 13.9713 21.1835 13.8163C21.0286 13.6601 20.8442 13.5361 20.6411 13.4515C20.438 13.3669 20.2202 13.3233 20.0002 13.3233C19.7801 13.3233 19.5623 13.3669 19.3592 13.4515C19.1561 13.5361 18.9718 13.6601 18.8168 13.8163L13.8168 18.8163ZM3.3335 19.9997C3.3335 23.296 4.31098 26.5184 6.14234 29.2592C7.97369 32 10.5767 34.1362 13.6221 35.3977C16.6675 36.6591 20.0187 36.9892 23.2517 36.3461C26.4847 35.703 29.4544 34.1157 31.7853 31.7848C34.1162 29.4539 35.7035 26.4842 36.3466 23.2512C36.9897 20.0182 36.6596 16.6671 35.3982 13.6216C34.1367 10.5762 32.0005 7.9732 29.2597 6.14185C26.5188 4.31049 23.2965 3.33301 20.0002 3.33301C17.8115 3.33301 15.6442 3.7641 13.6221 4.60168C11.6 5.43926 9.76269 6.66692 8.21505 8.21456C5.08944 11.3402 3.3335 15.5794 3.3335 19.9997V19.9997ZM33.3335 19.9997C33.3335 22.6368 32.5515 25.2146 31.0864 27.4073C29.6213 29.5999 27.539 31.3089 25.1026 32.3181C22.6663 33.3272 19.9854 33.5913 17.399 33.0768C14.8125 32.5623 12.4368 31.2925 10.5721 29.4278C8.70737 27.5631 7.4375 25.1873 6.92303 22.6009C6.40856 20.0145 6.6726 17.3336 7.68177 14.8972C8.69094 12.4609 10.3999 10.3785 12.5926 8.91341C14.7852 7.44833 17.3631 6.66634 20.0002 6.66634C23.5364 6.66634 26.9278 8.0711 29.4283 10.5716C31.9287 13.0721 33.3335 16.4635 33.3335 19.9997Z" fill="black"/>
			</svg>					
		</button>
	</div>
{/snippet}

<!-- Navigation buttons snippet -->
{#snippet navButtons()}
	<div class="nav-buttons">
		<!-- Show explanation button -->
		<Button --button-bg-color=var(--sky-blue) disabled={isAnswerUnanswered} onclick={showExplanation}>
			<svg width="36" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M33.0651 30.4351L27.5001 24.9151C29.6602 22.2217 30.7064 18.803 30.4233 15.3621C30.1403 11.9211 28.5496 8.71927 25.9784 6.41507C23.4072 4.11086 20.0508 2.87937 16.5995 2.97381C13.1482 3.06825 9.86418 4.48144 7.42281 6.92281C4.98144 9.36418 3.56825 12.6482 3.47381 16.0995C3.37937 19.5508 4.61086 22.9072 6.91507 25.4784C9.21927 28.0496 12.4211 29.6403 15.8621 29.9233C19.303 30.2064 22.7217 29.1602 25.4151 27.0001L30.9351 32.5201C31.0745 32.6607 31.2404 32.7723 31.4232 32.8484C31.606 32.9246 31.8021 32.9638 32.0001 32.9638C32.1981 32.9638 32.3942 32.9246 32.577 32.8484C32.7598 32.7723 32.9257 32.6607 33.0651 32.5201C33.3355 32.2404 33.4866 31.8666 33.4866 31.4776C33.4866 31.0886 33.3355 30.7148 33.0651 30.4351ZM17.0001 27.0001C14.9234 27.0001 12.8933 26.3843 11.1666 25.2305C9.4399 24.0768 8.09409 22.4369 7.29937 20.5183C6.50465 18.5997 6.29671 16.4885 6.70186 14.4517C7.107 12.4149 8.10703 10.5439 9.57548 9.07548C11.0439 7.60703 12.9149 6.607 14.9517 6.20186C16.9885 5.79671 19.0997 6.00465 21.0183 6.79937C22.9369 7.59409 24.5768 8.9399 25.7305 10.6666C26.8843 12.3933 27.5001 14.4234 27.5001 16.5001C27.5001 19.2849 26.3939 21.9556 24.4247 23.9247C22.4556 25.8939 19.7849 27.0001 17.0001 27.0001Z" fill="black"/>
			</svg>
		</Button>

		<!-- Next question button -->
		<Button --button-bg-color=var(--aquamarine) disabled={isAnswerUnanswered || loading} onclick={nextQuestion}>
			<svg width="36" height="36" viewBox="0 0 37 36" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M23.6431 16.9353L15.1531 8.4603C15.0137 8.31971 14.8478 8.20812 14.665 8.13196C14.4822 8.05581 14.2861 8.0166 14.0881 8.0166C13.8901 8.0166 13.694 8.05581 13.5112 8.13196C13.3284 8.20812 13.1625 8.31971 13.0231 8.4603C12.7437 8.74134 12.5869 9.12152 12.5869 9.5178C12.5869 9.91408 12.7437 10.2943 13.0231 10.5753L20.4481 18.0753L13.0231 25.5003C12.7437 25.7813 12.5869 26.1615 12.5869 26.5578C12.5869 26.9541 12.7437 27.3343 13.0231 27.6153C13.162 27.757 13.3277 27.8698 13.5105 27.947C13.6933 28.0243 13.8896 28.0645 14.0881 28.0653C14.2866 28.0645 14.4829 28.0243 14.6657 27.947C14.8485 27.8698 15.0142 27.757 15.1531 27.6153L23.6431 19.1403C23.7954 18.9998 23.9169 18.8294 24 18.6396C24.0831 18.4499 24.126 18.245 24.126 18.0378C24.126 17.8306 24.0831 17.6257 24 17.436C23.9169 17.2462 23.7954 17.0758 23.6431 16.9353V16.9353Z" fill="black"/>
			</svg>
		</Button>
	</div>
{/snippet}

<!-- Progress tracking snippet -->
{#snippet progress()}
	<div class="progress">
		{#each wasAnswersCorrect as answer}
			{#if answer}
				<!-- Correct answer indicator -->
				<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g filter="url(#filter0_d_4249_5345)">
					<rect width="24" height="24" rx="4" fill="#55ECB2"/>
					<rect x="0.5" y="0.5" width="23" height="23" rx="3.5" stroke="black"/>
					<path d="M18.7099 7.20986C18.617 7.11613 18.5064 7.04174 18.3845 6.99097C18.2627 6.9402 18.132 6.91406 17.9999 6.91406C17.8679 6.91406 17.7372 6.9402 17.6154 6.99097C17.4935 7.04174 17.3829 7.11613 17.29 7.20986L9.83995 14.6699L6.70995 11.5299C6.61343 11.4366 6.49949 11.3633 6.37463 11.3141C6.24978 11.2649 6.11645 11.2408 5.98227 11.2431C5.84809 11.2454 5.71568 11.2741 5.5926 11.3276C5.46953 11.3811 5.35819 11.4583 5.26495 11.5549C5.17171 11.6514 5.0984 11.7653 5.04919 11.8902C4.99999 12.015 4.97586 12.1484 4.97818 12.2825C4.9805 12.4167 5.00923 12.5491 5.06272 12.6722C5.11622 12.7953 5.19343 12.9066 5.28995 12.9999L9.12995 16.8399C9.22291 16.9336 9.33351 17.008 9.45537 17.0588C9.57723 17.1095 9.70794 17.1357 9.83995 17.1357C9.97196 17.1357 10.1027 17.1095 10.2245 17.0588C10.3464 17.008 10.457 16.9336 10.55 16.8399L18.7099 8.67986C18.8115 8.58622 18.8925 8.47257 18.9479 8.34607C19.0033 8.21957 19.0319 8.08296 19.0319 7.94486C19.0319 7.80676 19.0033 7.67015 18.9479 7.54365C18.8925 7.41715 18.8115 7.3035 18.7099 7.20986Z" fill="black"/>
					</g>
					<defs>
					<filter id="filter0_d_4249_5345" x="0" y="0" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feFlood flood-opacity="0" result="BackgroundImageFix"/>
					<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
					<feOffset dx="4" dy="4"/>
					<feComposite in2="hardAlpha" operator="out"/>
					<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
					<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4249_5345"/>
					<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4249_5345" result="shape"/>
					</filter>
					</defs>
				</svg>
			{:else}
				<!-- Incorrect answer indicator -->
				<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
					<g filter="url(#filter0_d_4249_5340)">
					<rect width="24" height="24" rx="4" fill="#EB47AB"/>
					<rect x="0.5" y="0.5" width="23" height="23" rx="3.5" stroke="black"/>
					<path d="M13.4099 12.0002L17.7099 7.71019C17.8982 7.52188 18.004 7.26649 18.004 7.00019C18.004 6.73388 17.8982 6.47849 17.7099 6.29019C17.5216 6.10188 17.2662 5.99609 16.9999 5.99609C16.7336 5.99609 16.4782 6.10188 16.2899 6.29019L11.9999 10.5902L7.70994 6.29019C7.52164 6.10188 7.26624 5.99609 6.99994 5.99609C6.73364 5.99609 6.47824 6.10188 6.28994 6.29019C6.10164 6.47849 5.99585 6.73388 5.99585 7.00019C5.99585 7.26649 6.10164 7.52188 6.28994 7.71019L10.5899 12.0002L6.28994 16.2902C6.19621 16.3831 6.12182 16.4937 6.07105 16.6156C6.02028 16.7375 5.99414 16.8682 5.99414 17.0002C5.99414 17.1322 6.02028 17.2629 6.07105 17.3848C6.12182 17.5066 6.19621 17.6172 6.28994 17.7102C6.3829 17.8039 6.4935 17.8783 6.61536 17.9291C6.73722 17.9798 6.86793 18.006 6.99994 18.006C7.13195 18.006 7.26266 17.9798 7.38452 17.9291C7.50638 17.8783 7.61698 17.8039 7.70994 17.7102L11.9999 13.4102L16.2899 17.7102C16.3829 17.8039 16.4935 17.8783 16.6154 17.9291C16.7372 17.9798 16.8679 18.006 16.9999 18.006C17.132 18.006 17.2627 17.9798 17.3845 17.9291C17.5064 17.8783 17.617 17.8039 17.7099 17.7102C17.8037 17.6172 17.8781 17.5066 17.9288 17.3848C17.9796 17.2629 18.0057 17.1322 18.0057 17.0002C18.0057 16.8682 17.9796 16.7375 17.9288 16.6156C17.8781 16.4937 17.8037 16.3831 17.7099 16.2902L13.4099 12.0002Z" fill="black"/>
					</g>
					<defs>
					<filter id="filter0_d_4249_5340" x="0" y="0" width="28" height="28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feFlood flood-opacity="0" result="BackgroundImageFix"/>
					<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
					<feOffset dx="4" dy="4"/>
					<feComposite in2="hardAlpha" operator="out"/>
					<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 1 0"/>
					<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4249_5340"/>
					<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_4249_5340" result="shape"/>
					</filter>
					</defs>
				</svg>
			{/if}
		{/each}
	</div>
{/snippet}

<!-- Annotation Panel snippet -->
{#snippet annotatePanel()}
	<!-- Header with back button -->
	<div class="annotate-header flex flex-row items-center gap-4">
		<button class="back-button" onclick={closeAnnotate} aria-label="Back">
			<svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path d="M13.8168 18.8163C13.6651 18.9748 13.5462 19.1618 13.4668 19.3663C13.3001 19.7721 13.3001 20.2272 13.4668 20.633C13.5462 20.8376 13.6651 21.0245 13.8168 21.183L18.8168 26.183C19.1307 26.4968 19.5563 26.6732 20.0002 26.6732C20.444 26.6732 20.8697 26.4968 21.1835 26.183C21.4973 25.8692 21.6736 25.4435 21.6736 24.9997C21.6736 24.5558 21.4973 24.1302 21.1835 23.8163L19.0168 21.6663H25.0002C25.4422 21.6663 25.8661 21.4907 26.1787 21.1782C26.4912 20.8656 26.6668 20.4417 26.6668 19.9997C26.6668 19.5576 26.4912 19.1337 26.1787 18.8212C25.8661 18.5086 25.4422 18.333 25.0002 18.333H19.0168L21.1835 16.183C21.3397 16.0281 21.4637 15.8437 21.5483 15.6406C21.6329 15.4375 21.6765 15.2197 21.6765 14.9997C21.6765 14.7797 21.6329 14.5618 21.5483 14.3587C21.4637 14.1556 21.3397 13.9713 21.1835 13.8163C21.0286 13.6601 20.8442 13.5361 20.6411 13.4515C20.438 13.3669 20.2202 13.3233 20.0002 13.3233C19.7801 13.3233 19.5623 13.3669 19.3592 13.4515C19.1561 13.5361 18.9718 13.6601 18.8168 13.8163L13.8168 18.8163ZM3.3335 19.9997C3.3335 23.296 4.31098 26.5184 6.14234 29.2592C7.97369 32 10.5767 34.1362 13.6221 35.3977C16.6675 36.6591 20.0187 36.9892 23.2517 36.3461C26.4847 35.703 29.4544 34.1157 31.7853 31.7848C34.1162 29.4539 35.7035 26.4842 36.3466 23.2512C36.9897 20.0182 36.6596 16.6671 35.3982 13.6216C34.1367 10.5762 32.0005 7.9732 29.2597 6.14185C26.5188 4.31049 23.2965 3.33301 20.0002 3.33301C17.8115 3.33301 15.6442 3.7641 13.6221 4.60168C11.6 5.43926 9.76269 6.66692 8.21505 8.21456C5.08944 11.3402 3.3335 15.5794 3.3335 19.9997V19.9997ZM33.3335 19.9997C33.3335 22.6368 32.5515 25.2146 31.0864 27.4073C29.6213 29.5999 27.539 31.3089 25.1026 32.3181C22.6663 33.3272 19.9854 33.5913 17.399 33.0768C14.8125 32.5623 12.4368 31.2925 10.5721 29.4278C8.70737 27.5631 7.4375 25.1873 6.92303 22.6009C6.40856 20.0145 6.6726 17.3336 7.68177 14.8972C8.69094 12.4609 10.3999 10.3785 12.5926 8.91341C14.7852 7.44833 17.3631 6.66634 20.0002 6.66634C23.5364 6.66634 26.9278 8.0711 29.4283 10.5716C31.9287 13.0721 33.3335 16.4635 33.3335 19.9997Z" fill="black"/>
			</svg>					
		</button>
		<H5>Annotate</H5>
	</div>

	{#if $selectedRange[0]}
		<!-- Highlighted word -->
		<div class="highlighted-word">
			{truncateText(selectedText)}
		</div>

		<!-- Comment section -->
		<div class="comment-section">
			<textarea class="comment-input" placeholder="Add your comment here..." bind:value={annoText}></textarea>
		</div>

		<!-- Add to vocab button -->
		<div class="vocab-button-container flex justify-center gap-4">
			<!-- <Button>Add to vocab</Button> -->
			<button onclick={onDeleteClick} aria-label="Delete annotation">
				<svg  width="30" height="30" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
					<path d="M10 18C10.2652 18 10.5196 17.8946 10.7071 17.7071C10.8946 17.5196 11 17.2652 11 17V11C11 10.7348 10.8946 10.4804 10.7071 10.2929C10.5196 10.1054 10.2652 10 10 10C9.73478 10 9.48043 10.1054 9.29289 10.2929C9.10536 10.4804 9 10.7348 9 11V17C9 17.2652 9.10536 17.5196 9.29289 17.7071C9.48043 17.8946 9.73478 18 10 18ZM20 6H16V5C16 4.20435 15.6839 3.44129 15.1213 2.87868C14.5587 2.31607 13.7956 2 13 2H11C10.2044 2 9.44129 2.31607 8.87868 2.87868C8.31607 3.44129 8 4.20435 8 5V6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7C3 7.26522 3.10536 7.51957 3.29289 7.70711C3.48043 7.89464 3.73478 8 4 8H5V19C5 19.7956 5.31607 20.5587 5.87868 21.1213C6.44129 21.6839 7.20435 22 8 22H16C16.7956 22 17.5587 21.6839 18.1213 21.1213C18.6839 20.5587 19 19.7956 19 19V8H20C20.2652 8 20.5196 7.89464 20.7071 7.70711C20.8946 7.51957 21 7.26522 21 7C21 6.73478 20.8946 6.48043 20.7071 6.29289C20.5196 6.10536 20.2652 6 20 6ZM10 5C10 4.73478 10.1054 4.48043 10.2929 4.29289C10.4804 4.10536 10.7348 4 11 4H13C13.2652 4 13.5196 4.10536 13.7071 4.29289C13.8946 4.48043 14 4.73478 14 5V6H10V5ZM17 19C17 19.2652 16.8946 19.5196 16.7071 19.7071C16.5196 19.8946 16.2652 20 16 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19V8H17V19ZM14 18C14.2652 18 14.5196 17.8946 14.7071 17.7071C14.8946 17.5196 15 17.2652 15 17V11C15 10.7348 14.8946 10.4804 14.7071 10.2929C14.5196 10.1054 14.2652 10 14 10C13.7348 10 13.4804 10.1054 13.2929 10.2929C13.1054 10.4804 13 10.7348 13 11V17C13 17.2652 13.1054 17.5196 13.2929 17.7071C13.4804 17.8946 13.7348 18 14 18Z" fill="#000"/>
				</svg>
			</button>
		</div>
	{:else}
		<div class="no-selection flex-grow-[1] flex justify-center items-center text-center">
			<P1>Select a word to annotate.</P1>
		</div>
	{/if}
{/snippet}

<style>
	.qb-root {
        min-height: 100vh;
        width: 100%;
		background: var(--very-light-sky-blue);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 3rem 4rem;
		gap: 2rem;
	}

	.qb-difficulty-row {
		display: flex;
		flex-wrap: wrap;
		gap: 1rem;
		align-items: center;
		justify-content: start;
		width: 100%;
		max-width: 32rem;
	}

	.dropdown-row {
		display: inline-flex;
		gap: 1rem;
		align-items: center;
		width: 100%;
		max-width: 32rem;
	}

	.qb-selectors-row {
		width: 100%;
		display: flex;
		gap: 2rem;
		justify-content: center;
		flex-wrap: wrap;
	}

	.qb-selector {
		background: var(--white);
		border: 1px solid var(--pitch-black);
		border-radius: 1rem;
		box-shadow: 0.25rem 0.25rem 0px var(--pitch-black);
		padding: 2rem 2.5rem 2rem 2.5rem;
		width: 100%;
		height: fit-content;
		max-height: 20rem;
        max-width: 32rem;
		display: flex;
		flex-direction: column;
		gap: 0.5rem;
	}

	.qb-selector-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 1.1rem;
	}

	hr {
		border: 1px solid var(--pitch-black);
	}

	.qb-selector-list {
		display: flex;
		flex-direction: column;
		gap: 0.75rem;
		height: 100%;
		overflow-y: auto;
		scrollbar-color: var(--sky-blue) var(--light-sky-blue);
		scrollbar-width: thin;
	}

	.qb-checkbox-row {
		display: flex;
		align-items: start;
		gap: 1rem;
		padding-right: 0.5rem;
	}

	.question-scene {
		display: inline-flex;
		flex-direction: row;
		justify-content: center;
		gap: 1rem;
		height: 100vh;
	}

	.progress-container {
		display: flex;
		flex-direction: column;
		gap: 2rem;
		width: 100%;
		max-width: 17rem;
		height: 100%;
		border: 1.5px solid var(--pitch-black);
		border-radius: 1rem;
		box-shadow: 0.25rem 0.25rem 0px var(--pitch-black);
		padding: 2rem 1rem;
		background: white;
		overflow-y: auto;
		scrollbar-color: var(--sky-blue) var(--light-sky-blue);
		scrollbar-width: thin;
	}

	.top {
		display: inline-flex;
		align-items: center;
		justify-content: space-between;
	}

	.progress {
		display: inline-flex;
		flex-wrap: wrap;
		align-content: start;
		justify-content: center;
		gap: 0.25rem;
		padding: 0 1rem;
		height: 100%;
	}

	.nav-buttons {
		display: inline-flex;
		gap: 0.625rem;
		justify-content: center;
	}

	.top-mobile-container {
		display: inline-flex;
		justify-content: space-between;
		width: 100%;
	}

	@media (max-width: 1200px) {
		.question-scene {
			flex-direction: column;
		}
		
		.progress-container {
			max-width: 100%;
			height: fit-content;
			flex-direction: row;
			align-items: center;
			justify-content: space-between;
		}
	}

	@media (min-width: 540px) and (max-width: 1200px) {
		.progress {
			align-content: start;
			justify-content: center;
			width: 100%;
			max-height: 6rem;
			overflow-y: auto;
			padding: 0;
		}
	}

	@media (max-width: 960px) {
		.qb-root {
			padding: 2rem 1rem;
		}

		.qb-selector {
			padding: 1.5rem 1rem;
		}

		.progress-container {
			padding: 1rem;
		}
	}

	@media (max-width: 540px) {
		.qb-root {
			padding: 1rem;
		}

		.progress-container {
			display: flex;
			flex-direction: column;
			gap: 2rem;
			overflow-y: initial;
		}
	}

	/* Annotation Panel Styles */
	.back-button {
		background: none;
		border: none;
		cursor: pointer;
		padding: 0;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 40px;
		height: 40px;
	}

	.back-button:hover {
		opacity: 0.7;
	}

	.highlighted-word {
		font-family: 'Open Sans', sans-serif;
		font-weight: 400;
		font-size: 20px;
		line-height: 30px;
		color: var(--pitch-black, #000000);
		text-align: center;
		padding: 0 0.75rem;
	}

	.comment-section {
		flex: 1;
		display: flex;
		flex-direction: column;
		gap: 0.625rem;
		padding: 0 0.5rem;
	}

	.comment-input {
		flex: 1;
		border: 1px solid var(--pitch-black, #000000);
		border-radius: 0.5rem;
		padding: 0.625rem;
		font-family: 'Open Sans', sans-serif;
		font-size: 18px;
		resize: none;
		min-height: 100px;
	}

	.comment-input:focus {
		outline: none;
		box-shadow: 0 0 0 2px var(--sky-blue, #66e2ff);
	}
</style>
